'use client';

import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';
import Image from 'next/image';

interface LogoProps {
  className?: string;
  width?: number;
  height?: number;
  showText?: boolean;
}

export default function Logo({ 
  className = "", 
  width = 32, 
  height = 32, 
  showText = true 
}: LogoProps) {
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Show fallback during hydration
  if (!mounted) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
          <span className="text-primary-foreground font-bold text-sm">K</span>
        </div>
        {showText && (
          <span className="text-xl font-bold text-foreground">KickoffScore</span>
        )}
      </div>
    );
  }

  const logoSrc = resolvedTheme === 'dark' 
    ? '/dark-mode-logo.svg' 
    : '/light-mode-logo.svg';

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className="relative" style={{ width, height }}>
        <Image
          src={logoSrc}
          alt="KickoffScore"
          width={width}
          height={height}
          className="object-contain"
          priority
        />
      </div>
      {showText && (
        <span className="text-xl font-bold text-foreground">KickoffScore</span>
      )}
    </div>
  );
}
