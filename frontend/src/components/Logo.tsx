'use client';

import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';
import Image from 'next/image';

interface LogoProps {
  className?: string;
  width?: number;
  height?: number;
}

export default function Logo({
  className = "",
  width = 120,
  height = 40
}: LogoProps) {
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Show fallback during hydration
  if (!mounted) {
    return (
      <div className={`flex items-center ${className}`}>
        <div className="bg-primary rounded-full flex items-center justify-center" style={{ width, height }}>
          <span className="text-primary-foreground font-bold text-lg">K</span>
        </div>
      </div>
    );
  }

  const logoSrc = resolvedTheme === 'dark'
    ? '/dark-mode-logo.svg'
    : '/light-mode-logo.svg';

  return (
    <div className={`flex items-center ${className}`}>
      <div className="relative" style={{ width, height }}>
        <Image
          src={logoSrc}
          alt="KickoffScore"
          width={width}
          height={height}
          className="object-contain"
          priority
        />
      </div>
    </div>
  );
}
