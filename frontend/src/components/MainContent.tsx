'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { ChevronLeft, ChevronRight, Search, ChevronDown } from 'lucide-react';
import { format, addDays, subDays, isToday, isTomorrow, isYesterday } from 'date-fns';
import { useCallback } from 'react';
import { apiService, Fixture, handleApiError } from '@/lib/api';
import { useLiveFixtures } from '@/hooks/useSocket';
import { groupFixturesByLeague, LeagueGroup } from '@/lib/leagueTiers';
import { Calendar } from '@/components/ui/calendar';
import { Popover } from '@/components/ui/popover';

// Remove the local Fixture interface since we're importing it from api.ts

interface MainContentProps {
  selectedLeagueId?: number | null;
  onClearLeagueFilter?: () => void;
}

type FilterType = 'all' | 'live' | 'finished' | 'upcoming' | 'by-time';

export default function MainContent({ selectedLeagueId, onClearLeagueFilter }: MainContentProps) {
  // Initialize selectedDate from localStorage or default to today
  const [selectedDate, setSelectedDate] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('kickoffscore-selected-date');
      if (saved) {
        return new Date(saved);
      }
    }
    return new Date();
  });
  const [fixtures, setFixtures] = useState<Fixture[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);
  const [activeFilter, setActiveFilter] = useState<FilterType>('all');
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  // Use WebSocket for live fixtures
  const { liveFixtures } = useLiveFixtures();

  // Set mounted state to prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);



  // Fetch fixtures data from API
  useEffect(() => {
    const fetchFixtures = async () => {
      setLoading(true);
      setError(null);

      try {
        const dateString = format(selectedDate, 'yyyy-MM-dd');

        const fixturesData = await apiService.getFixtures(dateString);
        setFixtures(fixturesData);
      } catch (err) {
        const errorMessage = handleApiError(err);
        setError(errorMessage);
        console.error('Failed to fetch fixtures:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchFixtures();
  }, [selectedDate]);

  // Update fixtures with live data when available
  useEffect(() => {
    if (liveFixtures.length > 0) {
      setFixtures(prevFixtures => {
        const updatedFixtures = [...prevFixtures];

        liveFixtures.forEach(liveFixture => {
          const index = updatedFixtures.findIndex(f => f._id === liveFixture._id);
          if (index !== -1) {
            updatedFixtures[index] = liveFixture;
          }
        });

        return updatedFixtures;
      });
    }
  }, [liveFixtures]);

  // Helper function to check fixture status
  const getFixtureStatus = (fixture: Fixture): FilterType => {
    const status = fixture.fixture.status.short;

    // Live statuses
    if (['1H', '2H', 'LIVE', 'HT', 'ET', 'BT', 'P', 'SUSP', 'INT'].includes(status)) {
      return 'live';
    }

    // Finished statuses
    if (['FT', 'AET', 'PEN', 'PST', 'CANC', 'ABD', 'AWD', 'WO'].includes(status)) {
      return 'finished';
    }

    // Upcoming statuses
    if (['NS', 'TBD'].includes(status)) {
      return 'upcoming';
    }

    return 'upcoming'; // Default fallback
  };

  // Filter fixtures based on search query, selected league, and status filter
  const filteredFixtures = fixtures.filter(fixture => {
    // Filter by league if one is selected
    if (selectedLeagueId && fixture.league.id !== selectedLeagueId) {
      return false;
    }

    // Filter by status only if not showing all or by-time
    if (activeFilter !== 'all' && activeFilter !== 'by-time' && getFixtureStatus(fixture) !== activeFilter) {
      return false;
    }

    // Filter by search query if one exists
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase();
    return (
      fixture.teams.home.name.toLowerCase().includes(query) ||
      fixture.teams.away.name.toLowerCase().includes(query) ||
      fixture.league.name.toLowerCase().includes(query) ||
      fixture.league.country.toLowerCase().includes(query)
    );
  });

  // Sort fixtures by time if "by-time" filter is active
  const sortedFixtures = activeFilter === 'by-time'
    ? [...filteredFixtures].sort((a, b) => {
        const timeA = new Date(a.fixture.date).getTime();
        const timeB = new Date(b.fixture.date).getTime();
        return timeA - timeB;
      })
    : filteredFixtures;

  // Group fixtures by league and sort by tier/priority (only if not "by-time")
  const groupedFixtures = activeFilter === 'by-time'
    ? []
    : groupFixturesByLeague(sortedFixtures);



  const handlePrevDate = () => {
    const newDate = subDays(selectedDate, 1);
    if (typeof window !== 'undefined') {
      localStorage.setItem('kickoffscore-selected-date', newDate.toISOString());
    }
    setSelectedDate(newDate);
  };

  const handleNextDate = () => {
    const newDate = addDays(selectedDate, 1);
    if (typeof window !== 'undefined') {
      localStorage.setItem('kickoffscore-selected-date', newDate.toISOString());
    }
    setSelectedDate(newDate);
  };

  // Helper function to get the display text for the selected date
  const getDateDisplayText = () => {
    if (isToday(selectedDate)) {
      return 'Today';
    } else if (isTomorrow(selectedDate)) {
      return 'Tomorrow';
    } else if (isYesterday(selectedDate)) {
      return 'Yesterday';
    } else {
      return format(selectedDate, 'MMM d, EEE');
    }
  };

  // Handle calendar date selection
  const handleCalendarDateSelect = useCallback((date: Date | undefined) => {
    if (date) {
      // Save to localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('kickoffscore-selected-date', date.toISOString());
      }
      setSelectedDate(date);
      // Close calendar after a short delay to let user see the date change
      setTimeout(() => {
        setIsCalendarOpen(false);
      }, 300);
    }
  }, []);

  // Function to get country flag URL using API-Sports media
  const getCountryFlagUrl = (countryName: string) => {
    // Convert country name to country code for API-Sports flags
    const countryMappings: { [key: string]: string } = {
      'england': 'gb',
      'scotland': 'gb',
      'wales': 'gb',
      'northern ireland': 'gb',
      'united kingdom': 'gb',
      'usa': 'us',
      'united states': 'us',
      'south korea': 'kr',
      'north korea': 'kp',
      'czech republic': 'cz',
      'bosnia and herzegovina': 'ba',
      'trinidad and tobago': 'tt',
      'costa rica': 'cr',
      'el salvador': 'sv',
      'saudi arabia': 'sa',
      'united arab emirates': 'ae',
      'ivory coast': 'ci',
      'south africa': 'za',
      'world': 'fifa'
    };

    const countryCode = countryMappings[countryName.toLowerCase()] ||
                       countryName.toLowerCase().replace(/\s+/g, '-').substring(0, 2);

    return `https://media.api-sports.io/flags/${countryCode}.svg`;
  };

  const LeagueHeader = ({ leagueGroup }: { leagueGroup: LeagueGroup }) => (
    <div className="flex items-center space-x-2 py-2 px-4 bg-muted border-b border-border">
      <div className="w-[18px] h-[18px] flex-shrink-0 relative">
        <Image
          src={getCountryFlagUrl(leagueGroup.leagueCountry)}
          alt={leagueGroup.leagueCountry}
          width={18}
          height={18}
          className="w-[18px] h-[18px] object-cover rounded-full border border-border"
          loading="lazy"
          onError={(e) => {
            // Keep the same circular styling and dimensions for fallback
            e.currentTarget.src = leagueGroup.leagueLogo;
            e.currentTarget.className = "w-[18px] h-[18px] object-cover rounded-full border border-border";
          }}
        />
      </div>
      <h3 className="text-sm font-medium text-foreground">{leagueGroup.leagueCountry} - {leagueGroup.leagueName}</h3>
    </div>
  );

  // Skeleton component for loading fixture cards
  const FixtureCardSkeleton = () => (
    <div className="bg-card border-b border-border">
      <div className="flex items-center py-3 px-4">
        {/* Favorite Icon and Status Column */}
        <div className="flex flex-col items-center mr-3">
          <div className="w-4 h-4 bg-muted rounded animate-pulse mb-1"></div>
          <div className="w-8 h-3 bg-muted rounded animate-pulse"></div>
        </div>

        {/* Teams Column */}
        <div className="flex-1 min-w-0">
          {/* Home Team Row */}
          <div className="flex items-center justify-between space-x-2 mb-1">
            <div className="flex items-center space-x-2 flex-1 min-w-0">
              <div className="w-[18px] h-[18px] bg-muted rounded animate-pulse flex-shrink-0"></div>
              <div className="h-4 bg-muted rounded animate-pulse w-24"></div>
            </div>
            <div className="w-4 h-4 bg-muted rounded animate-pulse flex-shrink-0"></div>
          </div>

          {/* Away Team Row */}
          <div className="flex items-center justify-between space-x-2">
            <div className="flex items-center space-x-2 flex-1 min-w-0">
              <div className="w-[18px] h-[18px] bg-muted rounded animate-pulse flex-shrink-0"></div>
              <div className="h-4 bg-muted rounded animate-pulse w-20"></div>
            </div>
            <div className="w-4 h-4 bg-muted rounded animate-pulse flex-shrink-0"></div>
          </div>
        </div>

        {/* Statistics/Odds Columns */}
        <div className="flex items-center space-x-3 md:space-x-6 ml-4 md:ml-6">
          <div className="min-w-[45px] md:min-w-[60px]">
            {/* Mobile: Two lines, Desktop: One line */}
            <div className="md:hidden">
              <div className="h-3 bg-muted rounded animate-pulse w-12 mb-1"></div>
              <div className="h-3 bg-muted rounded animate-pulse w-10"></div>
            </div>
            <div className="hidden md:block">
              <div className="h-3 bg-muted rounded animate-pulse w-16"></div>
            </div>
          </div>
          <div className="min-w-[32px] md:min-w-[40px]">
            <div className="h-4 bg-muted rounded animate-pulse w-8"></div>
          </div>
          <div className="min-w-[32px] md:min-w-[40px]">
            <div className="h-4 bg-muted rounded animate-pulse w-8"></div>
          </div>
        </div>
      </div>
    </div>
  );

  const FixtureCard = ({ fixture }: { fixture: Fixture; grouped?: boolean }) => {
    // Function to get display status/time
    const getStatusDisplay = () => {
      const status = fixture.fixture.status.short;
      const elapsed = fixture.fixture.status.elapsed;

      // Live statuses with elapsed time
      if ((status === '1H' || status === '2H' || status === 'LIVE') && elapsed) {
        return `${elapsed}'`;
      }

      // Other match statuses
      if (status === 'HT') return 'HT';
      if (status === 'FT') return 'FT';
      if (status === 'AET') return 'AET';
      if (status === 'PEN') return 'PEN';
      if (status === 'PST') return 'PST';
      if (status === 'CANC') return 'CANC';
      if (status === 'ABD') return 'ABD';
      if (status === 'AWD') return 'AWD';
      if (status === 'WO') return 'WO';
      if (status === 'TBD') return 'TBD';

      // For upcoming matches (NS), show time
      if (status === 'NS') {
        return mounted ? format(new Date(fixture.fixture.date), 'HH:mm') : '';
      }

      return status;
    };

    // Function to check if status should be red (live/in-play)
    const isLiveStatus = () => {
      const status = fixture.fixture.status.short;
      return ['1H', '2H', 'LIVE', 'HT', 'ET', 'BT', 'P', 'SUSP', 'INT'].includes(status);
    };

    return (
      <div className="bg-card border-b border-border hover:bg-muted/50 transition-colors">
        <div className="flex items-center py-3 px-4">
          {/* Favorite Icon and Status Column */}
          <div className="flex flex-col items-center mr-3">
            {/* Favorite Icon */}
            <button className="text-muted-foreground hover:text-yellow-500 transition-colors mb-1">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
              </svg>
            </button>
            {/* Status/Time Display */}
            <div className={`text-xs font-medium text-center min-w-[32px] ${
              isLiveStatus() ? 'text-red-500' : 'text-muted-foreground'
            }`}>
              {getStatusDisplay()}
            </div>
          </div>

          {/* Teams Column */}
          <div className="flex-1 min-w-0">
            {/* Home Team Row */}
            <div className="flex items-center justify-between space-x-2 mb-1">
              <div className="flex items-center space-x-2 flex-1 min-w-0">
                <div className="w-[18px] h-[18px] flex-shrink-0 relative">
                  <Image
                    src={fixture.teams.home.logo}
                    alt={fixture.teams.home.name}
                    width={18}
                    height={18}
                    className="w-[18px] h-[18px] object-contain"
                    loading="lazy"
                    onError={(e) => {
                      // Replace with team initial instead of hiding to maintain layout
                      const parent = e.currentTarget.parentElement;
                      if (parent) {
                        parent.innerHTML = `<div class="w-[18px] h-[18px] bg-muted rounded flex items-center justify-center flex-shrink-0"><span class="text-xs font-bold text-muted-foreground">${fixture.teams.home.name.charAt(0)}</span></div>`;
                      }
                    }}
                  />
                </div>
                <span className="text-xs md:text-sm text-foreground truncate">{fixture.teams.home.name}</span>
              </div>
              {/* Home Score */}
              <div className="text-sm font-semibold text-foreground ml-1 flex-shrink-0">
                {fixture.goals.home !== null ? fixture.goals.home : '-'}
              </div>
            </div>

            {/* Away Team Row */}
            <div className="flex items-center justify-between space-x-2">
              <div className="flex items-center space-x-2 flex-1 min-w-0">
                <div className="w-[18px] h-[18px] flex-shrink-0 relative">
                  <Image
                    src={fixture.teams.away.logo}
                    alt={fixture.teams.away.name}
                    width={18}
                    height={18}
                    className="w-[18px] h-[18px] object-contain"
                    loading="lazy"
                    onError={(e) => {
                      // Replace with team initial instead of hiding to maintain layout
                      const parent = e.currentTarget.parentElement;
                      if (parent) {
                        parent.innerHTML = `<div class="w-[18px] h-[18px] bg-muted rounded flex items-center justify-center flex-shrink-0"><span class="text-xs font-bold text-muted-foreground">${fixture.teams.away.name.charAt(0)}</span></div>`;
                      }
                    }}
                  />
                </div>
                <span className="text-xs md:text-sm text-foreground truncate">{fixture.teams.away.name}</span>
              </div>
              {/* Away Score */}
              <div className="text-sm font-semibold text-foreground ml-1 flex-shrink-0">
                {fixture.goals.away !== null ? fixture.goals.away : '-'}
              </div>
            </div>
          </div>

          {/* Statistics/Odds Columns - Centered between teams */}
          <div className="flex items-center space-x-3 md:space-x-6 ml-4 md:ml-6 text-xs">
            <div className="text-left min-w-[45px] md:min-w-[60px] leading-tight">
              <div className="text-muted-foreground">
                <span className="md:hidden">Number<br />of goals</span>
                <span className="hidden md:inline">Number of goals</span>
              </div>
            </div>
            <div className="text-center min-w-[32px] md:min-w-[40px]">
              <div className="text-primary font-medium">-3.5</div>
            </div>
            <div className="text-right min-w-[32px] md:min-w-[40px]">
              <div className="text-foreground font-medium">1.12</div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return (
      <div className="flex-1 px-0 md:px-2 py-4">
        <div className="mx-auto px-2 md:px-0" style={{ maxWidth: 'var(--container-2x)' }}>
          <div className="animate-pulse space-y-6">
            {/* Navigation Bar Container */}
            <div className="bg-card rounded-lg border border-border p-4">
              {/* Desktop Layout Skeleton */}
              <div className="hidden md:block">
                {/* Filter Buttons and Date Navigation */}
                <div className="flex items-center justify-between mb-4">
                  {/* Left side - Filter buttons skeleton */}
                  <div className="flex items-center space-x-2">
                    <div className="w-10 h-8 bg-muted rounded-lg"></div>
                    <div className="w-12 h-8 bg-muted rounded-lg"></div>
                    <div className="w-16 h-8 bg-muted rounded-lg"></div>
                    <div className="w-18 h-8 bg-muted rounded-lg"></div>
                    <div className="w-16 h-8 bg-muted rounded-lg"></div>
                  </div>
                  {/* Right side - Date navigation skeleton */}
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-muted rounded-lg"></div>
                    <div className="w-16 h-8 bg-muted rounded-lg"></div>
                    <div className="w-8 h-8 bg-muted rounded-lg"></div>
                  </div>
                </div>
                {/* Search Bar - Desktop only */}
                <div className="h-10 bg-muted rounded-lg"></div>
              </div>

              {/* Mobile Layout Skeleton */}
              <div className="md:hidden">
                {/* Filter Buttons - Mobile */}
                <div className="flex items-center space-x-2 mb-4 overflow-x-auto">
                  <div className="w-10 h-8 bg-muted rounded-lg"></div>
                  <div className="w-12 h-8 bg-muted rounded-lg"></div>
                  <div className="w-16 h-8 bg-muted rounded-lg"></div>
                  <div className="w-18 h-8 bg-muted rounded-lg"></div>
                  <div className="w-16 h-8 bg-muted rounded-lg"></div>
                </div>
                {/* Date Navigation - Mobile (centered) */}
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-8 h-8 bg-muted rounded-lg"></div>
                  <div className="w-16 h-8 bg-muted rounded-lg"></div>
                  <div className="w-8 h-8 bg-muted rounded-lg"></div>
                </div>
              </div>
            </div>



            {/* Fixtures Table */}
            <div className="space-y-4">
              {/* Create 3 skeleton league containers */}
              {Array.from({ length: 3 }).map((_, leagueIndex) => (
                <div key={`initial-league-skeleton-${leagueIndex}`} className="bg-card rounded-lg border border-border overflow-hidden">
                  {/* League Header Skeleton */}
                  <div className="flex items-center space-x-2 py-2 px-4 bg-muted border-b border-border">
                    <div className="w-[18px] h-[18px] bg-muted-foreground/20 rounded-full animate-pulse"></div>
                    <div className="h-4 bg-muted-foreground/20 rounded animate-pulse w-48"></div>
                  </div>
                  {/* Skeleton fixture cards for this league */}
                  {Array.from({ length: 2 + leagueIndex }).map((_, fixtureIndex) => (
                    <FixtureCardSkeleton key={`initial-fixture-skeleton-${leagueIndex}-${fixtureIndex}`} />
                  ))}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 px-0 md:px-2 py-4">
      <div className="mx-auto px-2 md:px-0" style={{ maxWidth: 'var(--container-2x)' }}>
        {/* Navigation Bar with Filter Buttons and Date Navigation */}
        <div className="bg-card rounded-lg border border-border p-4 mb-6">
          {/* Desktop Layout */}
          <div className="hidden md:block">
            {/* Filter Buttons and Date Navigation */}
            <div className="flex items-center justify-between mb-4">
              {/* Left side - Filter buttons */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setActiveFilter('all')}
                  className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors cursor-pointer ${
                    activeFilter === 'all'
                      ? 'text-black hover:bg-gray-100 dark:text-black dark:hover:bg-gray-100'
                      : 'bg-muted text-muted-foreground hover:bg-muted/80'
                  }`}
                  style={activeFilter === 'all' ? { backgroundColor: '#fff' } : {}}
                >
                  All
                </button>
                <button
                  onClick={() => setActiveFilter(activeFilter === 'live' ? 'all' : 'live')}
                  className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors cursor-pointer ${
                    activeFilter === 'live'
                      ? 'text-black hover:bg-gray-100 dark:text-black dark:hover:bg-gray-100'
                      : 'bg-muted text-muted-foreground hover:bg-muted/80'
                  }`}
                  style={activeFilter === 'live' ? { backgroundColor: '#fff' } : {}}
                >
                  Live
                </button>
                <button
                  onClick={() => setActiveFilter(activeFilter === 'finished' ? 'all' : 'finished')}
                  className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors cursor-pointer ${
                    activeFilter === 'finished'
                      ? 'text-black hover:bg-gray-100 dark:text-black dark:hover:bg-gray-100'
                      : 'bg-muted text-muted-foreground hover:bg-muted/80'
                  }`}
                  style={activeFilter === 'finished' ? { backgroundColor: '#fff' } : {}}
                >
                  Finished
                </button>
                <button
                  onClick={() => setActiveFilter(activeFilter === 'upcoming' ? 'all' : 'upcoming')}
                  className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors cursor-pointer ${
                    activeFilter === 'upcoming'
                      ? 'text-black hover:bg-gray-100 dark:text-black dark:hover:bg-gray-100'
                      : 'bg-muted text-muted-foreground hover:bg-muted/80'
                  }`}
                  style={activeFilter === 'upcoming' ? { backgroundColor: '#fff' } : {}}
                >
                  Upcoming
                </button>
                <button
                  onClick={() => setActiveFilter(activeFilter === 'by-time' ? 'all' : 'by-time')}
                  className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors cursor-pointer ${
                    activeFilter === 'by-time'
                      ? 'text-black hover:bg-gray-100 dark:text-black dark:hover:bg-gray-100'
                      : 'bg-muted text-muted-foreground hover:bg-muted/80'
                  }`}
                  style={activeFilter === 'by-time' ? { backgroundColor: '#fff' } : {}}
                >
                  By Time
                </button>
              </div>

              {/* Right side - Compact Date Navigation */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={handlePrevDate}
                  className="p-2 hover:bg-muted rounded-full transition-colors cursor-pointer bg-muted/50"
                >
                  <ChevronLeft className="w-4 h-4 text-muted-foreground" />
                </button>

                <Popover
                  open={isCalendarOpen}
                  onOpenChange={setIsCalendarOpen}
                  content={
                    <Calendar
                      mode="single"
                      selected={selectedDate}
                      onSelect={handleCalendarDateSelect}
                      defaultMonth={selectedDate}
                    />
                  }
                >
                  <div className="flex items-center space-x-1 rounded-lg px-3 py-2 hover:bg-muted/50 transition-colors cursor-pointer">
                    <span className="text-sm font-medium text-foreground">
                      {getDateDisplayText()}
                    </span>
                    <ChevronDown className="w-4 h-4 text-muted-foreground" />
                  </div>
                </Popover>

                <button
                  onClick={handleNextDate}
                  className="p-2 hover:bg-muted rounded-full transition-colors cursor-pointer bg-muted/50"
                >
                  <ChevronRight className="w-4 h-4 text-muted-foreground" />
                </button>
              </div>
            </div>

            {/* Search Bar - Desktop only */}
            <div className="flex items-center bg-muted rounded-lg px-3 py-2">
              <Search className="w-4 h-4 text-muted-foreground mr-2" />
              <input
                type="text"
                placeholder="Search fixtures, teams, leagues..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="bg-transparent outline-none text-sm flex-1 text-foreground placeholder-muted-foreground"
              />
            </div>
          </div>

          {/* Mobile Layout */}
          <div className="md:hidden">
            {/* Filter Buttons - Mobile */}
            <div className="flex items-center space-x-2 mb-4 overflow-x-auto">
              <button
                onClick={() => setActiveFilter('all')}
                className={`px-3 py-1.5 rounded-lg text-xs font-medium transition-colors whitespace-nowrap cursor-pointer ${
                  activeFilter === 'all'
                    ? 'text-black hover:bg-gray-100 dark:text-black dark:hover:bg-gray-100'
                    : 'bg-muted text-muted-foreground hover:bg-muted/80'
                }`}
                style={activeFilter === 'all' ? { backgroundColor: '#fff' } : {}}
              >
                All
              </button>
              <button
                onClick={() => setActiveFilter(activeFilter === 'live' ? 'all' : 'live')}
                className={`px-3 py-1.5 rounded-lg text-xs font-medium transition-colors whitespace-nowrap cursor-pointer ${
                  activeFilter === 'live'
                    ? 'text-black hover:bg-gray-100 dark:text-black dark:hover:bg-gray-100'
                    : 'bg-muted text-muted-foreground hover:bg-muted/80'
                }`}
                style={activeFilter === 'live' ? { backgroundColor: '#fff' } : {}}
              >
                Live
              </button>
              <button
                onClick={() => setActiveFilter(activeFilter === 'finished' ? 'all' : 'finished')}
                className={`px-3 py-1.5 rounded-lg text-xs font-medium transition-colors whitespace-nowrap cursor-pointer ${
                  activeFilter === 'finished'
                    ? 'text-black hover:bg-gray-100 dark:text-black dark:hover:bg-gray-100'
                    : 'bg-muted text-muted-foreground hover:bg-muted/80'
                }`}
                style={activeFilter === 'finished' ? { backgroundColor: '#fff' } : {}}
              >
                Finished
              </button>
              <button
                onClick={() => setActiveFilter(activeFilter === 'upcoming' ? 'all' : 'upcoming')}
                className={`px-3 py-1.5 rounded-lg text-xs font-medium transition-colors whitespace-nowrap cursor-pointer ${
                  activeFilter === 'upcoming'
                    ? 'text-black hover:bg-gray-100 dark:text-black dark:hover:bg-gray-100'
                    : 'bg-muted text-muted-foreground hover:bg-muted/80'
                }`}
                style={activeFilter === 'upcoming' ? { backgroundColor: '#fff' } : {}}
              >
                Upcoming
              </button>
              <button
                onClick={() => setActiveFilter(activeFilter === 'by-time' ? 'all' : 'by-time')}
                className={`px-3 py-1.5 rounded-lg text-xs font-medium transition-colors whitespace-nowrap cursor-pointer ${
                  activeFilter === 'by-time'
                    ? 'text-black hover:bg-gray-100 dark:text-black dark:hover:bg-gray-100'
                    : 'bg-muted text-muted-foreground hover:bg-muted/80'
                }`}
                style={activeFilter === 'by-time' ? { backgroundColor: '#fff' } : {}}
              >
                By Time
              </button>
            </div>

            {/* Date Navigation - Mobile (centered) */}
            <div className="flex items-center justify-center space-x-2">
              <button
                onClick={handlePrevDate}
                className="p-2 hover:bg-muted rounded-full transition-colors cursor-pointer bg-muted/50"
              >
                <ChevronLeft className="w-4 h-4 text-muted-foreground" />
              </button>

              <Popover
                open={isCalendarOpen}
                onOpenChange={setIsCalendarOpen}
                content={
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={handleCalendarDateSelect}
                    defaultMonth={selectedDate}
                  />
                }
              >
                <div className="flex items-center space-x-1 rounded-lg px-3 py-2 hover:bg-muted/50 transition-colors cursor-pointer">
                  <span className="text-sm font-medium text-foreground">
                    {getDateDisplayText()}
                  </span>
                  <ChevronDown className="w-4 h-4 text-muted-foreground" />
                </div>
              </Popover>

              <button
                onClick={handleNextDate}
                className="p-2 hover:bg-muted rounded-full transition-colors cursor-pointer bg-muted/50"
              >
                <ChevronRight className="w-4 h-4 text-muted-foreground" />
              </button>
            </div>
          </div>
        </div>



        {/* Fixtures List */}
        <div className="space-y-4">
          {selectedLeagueId && (
            <div className="flex items-center justify-end">
              <button
                onClick={onClearLeagueFilter}
                className="text-sm text-primary hover:text-primary/80 font-medium"
              >
                Show All Leagues
              </button>
            </div>
          )}

          {loading ? (
            <div className="space-y-4">
              {/* Create 3 skeleton league containers */}
              {Array.from({ length: 3 }).map((_, leagueIndex) => (
                <div key={`league-skeleton-${leagueIndex}`} className="bg-card rounded-lg border border-border overflow-hidden">
                  {/* League Header Skeleton */}
                  <div className="flex items-center space-x-2 py-2 px-4 bg-muted border-b border-border">
                    <div className="w-[18px] h-[18px] bg-muted-foreground/20 rounded-full animate-pulse"></div>
                    <div className="h-4 bg-muted-foreground/20 rounded animate-pulse w-48"></div>
                  </div>
                  {/* Skeleton fixture cards for this league */}
                  {Array.from({ length: 2 + leagueIndex }).map((_, fixtureIndex) => (
                    <FixtureCardSkeleton key={`fixture-skeleton-${leagueIndex}-${fixtureIndex}`} />
                  ))}
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <div className="text-destructive mb-2">Error loading fixtures</div>
              <div className="text-muted-foreground text-sm">{error}</div>
              <button
                onClick={() => window.location.reload()}
                className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
              >
                Retry
              </button>
            </div>
          ) : activeFilter === 'by-time' && sortedFixtures.length > 0 ? (
            <div className="bg-card rounded-lg border border-border overflow-hidden">
              <div>
                {sortedFixtures.map((fixture) => (
                  <FixtureCard key={fixture._id || fixture.id} fixture={fixture} grouped={false} />
                ))}
              </div>
            </div>
          ) : groupedFixtures.length > 0 ? (
            <div className="space-y-4">
              {groupedFixtures.map((leagueGroup) => (
                <div key={leagueGroup.leagueId} className="bg-card rounded-lg border border-border overflow-hidden">
                  <LeagueHeader leagueGroup={leagueGroup} />
                  <div>
                    {leagueGroup.fixtures.map((fixture) => (
                      <FixtureCard key={fixture._id || fixture.id} fixture={fixture} grouped={true} />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          ) : searchQuery ? (
            <div className="text-center py-8">
              <div className="text-muted-foreground">No fixtures found matching &quot;{searchQuery}&quot;</div>
              <div className="text-muted-foreground/60 text-sm mt-2">
                Try a different search term or clear the search to see all fixtures.
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-muted-foreground">No fixtures found for this date.</div>
              <div className="text-muted-foreground/60 text-sm mt-2">
                Try selecting a different date or check back later.
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
